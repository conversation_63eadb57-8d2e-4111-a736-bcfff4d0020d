#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试持仓同步中买入信息获取的脚本
"""

from datetime import datetime, timedelta
import json

def simulate_trade_reports():
    """模拟成交回报数据"""
    # 模拟最近几天的成交记录
    base_time = datetime.now() - timedelta(days=2)
    
    trades = [
        {
            'stock_code': '123456',
            'direction': 1,  # STOCK_BUY
            'volume': 500,
            'traded_price': 100.50,
            'trade_time': (base_time + timedelta(hours=1)).strftime('%H:%M:%S'),
            'order_id': 'ORDER_001',
            'amount': 50250.0,
            'fee': 5.03
        },
        {
            'stock_code': '123456',
            'direction': 1,  # STOCK_BUY
            'volume': 300,
            'traded_price': 101.20,
            'trade_time': (base_time + timedelta(hours=2)).strftime('%H:%M:%S'),
            'order_id': 'ORDER_002',
            'amount': 30360.0,
            'fee': 3.04
        },
        {
            'stock_code': '123456',
            'direction': 1,  # STOCK_BUY
            'volume': 200,
            'traded_price': 99.80,
            'trade_time': (base_time + timedelta(hours=3)).strftime('%H:%M:%S'),
            'order_id': 'ORDER_003',
            'amount': 19960.0,
            'fee': 2.00
        },
        {
            'stock_code': '789012',
            'direction': 1,  # STOCK_BUY
            'volume': 1000,
            'traded_price': 150.30,
            'trade_time': (base_time + timedelta(hours=4)).strftime('%H:%M:%S'),
            'order_id': 'ORDER_004',
            'amount': 150300.0,
            'fee': 15.03
        }
    ]
    
    return trades

def test_get_trade_info_from_reports(code, volume, trades):
    """测试从成交回报中获取买入信息的逻辑"""
    print(f"=== 测试获取 {code} {volume}股的买入信息 ===")
    
    # 过滤该股票的买入记录
    buy_trades = []
    for trade in trades:
        if trade['stock_code'] == code and trade['direction'] == 1:  # STOCK_BUY
            buy_trades.append(trade)
    
    if not buy_trades:
        print("❌ 未找到买入记录")
        return None
    
    print(f"找到 {len(buy_trades)} 条买入记录:")
    for i, trade in enumerate(buy_trades, 1):
        print(f"  {i}. 时间:{trade['trade_time']} 数量:{trade['volume']}股 价格:{trade['traded_price']:.3f} 委托号:{trade['order_id']}")
    
    # 按时间排序（最新的在前）
    buy_trades.sort(key=lambda x: x['trade_time'], reverse=True)
    
    # 尝试匹配持仓数量
    total_volume = 0
    matched_trades = []
    total_amount = 0
    total_fee = 0
    earliest_time = None
    
    print(f"\n匹配 {volume} 股持仓:")
    for trade in buy_trades:
        if total_volume >= volume:
            break
            
        # 计算需要的数量
        needed_volume = min(trade['volume'], volume - total_volume)
        
        matched_trades.append({
            'price': trade['traded_price'],
            'volume': needed_volume,
            'time': trade['trade_time'],
            'order_id': trade['order_id']
        })
        
        # 累计计算
        trade_amount = trade['traded_price'] * needed_volume
        total_amount += trade_amount
        total_volume += needed_volume
        
        # 计算手续费（按比例分配）
        proportional_fee = trade['fee'] * (needed_volume / trade['volume'])
        total_fee += proportional_fee
        
        # 记录最早的买入时间
        if not earliest_time or trade['trade_time'] < earliest_time:
            earliest_time = trade['trade_time']
        
        print(f"  匹配: {needed_volume}股 @ {trade['traded_price']:.3f} (委托号:{trade['order_id']})")
    
    # 计算加权平均价格
    weighted_price = total_amount / total_volume if total_volume > 0 else 0
    match_accuracy = total_volume / volume if volume > 0 else 0
    
    print(f"\n匹配结果:")
    print(f"  总匹配数量: {total_volume}股")
    print(f"  加权平均价格: {weighted_price:.3f}")
    print(f"  总金额: {total_amount:.2f}")
    print(f"  总手续费: {total_fee:.2f}")
    print(f"  最早买入时间: {earliest_time}")
    print(f"  匹配准确度: {match_accuracy:.1%}")
    
    # 如果匹配的数量与持仓数量相符或接近
    if abs(total_volume - volume) <= volume * 0.05:  # 允许5%的误差
        print("✅ 匹配成功")
        return {
            'buy_price': weighted_price,
            'buy_time': earliest_time,
            'quantity': volume,
            'fee': total_fee,
            'actual_amount': total_amount,
            'matched_trades': matched_trades,
            'match_accuracy': match_accuracy
        }
    else:
        print("❌ 匹配失败：数量差异过大")
        return None

def test_different_scenarios():
    """测试不同的持仓同步场景"""
    trades = simulate_trade_reports()
    
    print("模拟成交回报数据:")
    for trade in trades:
        direction_text = "买入" if trade['direction'] == 1 else "卖出"
        print(f"  {trade['stock_code']} {direction_text} {trade['volume']}股 @ {trade['traded_price']:.3f} "
              f"时间:{trade['trade_time']} 委托号:{trade['order_id']}")
    print()
    
    # 测试场景1：完全匹配
    print("场景1：完全匹配 - 持仓1000股，成交记录正好1000股")
    result1 = test_get_trade_info_from_reports('123456', 1000, trades)
    print()
    
    # 测试场景2：部分匹配
    print("场景2：部分匹配 - 持仓800股，成交记录1000股")
    result2 = test_get_trade_info_from_reports('123456', 800, trades)
    print()
    
    # 测试场景3：单笔匹配
    print("场景3：单笔匹配 - 持仓1000股，只有一笔成交记录")
    result3 = test_get_trade_info_from_reports('789012', 1000, trades)
    print()
    
    # 测试场景4：无匹配记录
    print("场景4：无匹配记录 - 持仓股票没有成交记录")
    result4 = test_get_trade_info_from_reports('999999', 500, trades)
    print()
    
    # 测试场景5：数量不足
    print("场景5：数量不足 - 持仓1500股，但成交记录只有1000股")
    result5 = test_get_trade_info_from_reports('123456', 1500, trades)
    print()

def simulate_position_sync_scenarios():
    """模拟持仓同步的不同场景"""
    print("=== 持仓同步场景模拟 ===\n")
    
    scenarios = [
        {
            'name': '场景1：服务器有成本价',
            'server_cost_price': 100.50,
            'has_trade_report': True,
            'description': '服务器返回了成本价，同时能从成交回报获取信息'
        },
        {
            'name': '场景2：无成本价但有成交回报',
            'server_cost_price': None,
            'has_trade_report': True,
            'description': '服务器没有成本价，但能从成交回报获取真实买入信息'
        },
        {
            'name': '场景3：无成本价无成交回报',
            'server_cost_price': None,
            'has_trade_report': False,
            'description': '服务器没有成本价，也无法从成交回报获取信息，使用当前价格'
        },
        {
            'name': '场景4：成本价与成交回报不一致',
            'server_cost_price': 105.00,
            'has_trade_report': True,
            'description': '服务器成本价与成交回报计算的价格不一致'
        }
    ]
    
    for scenario in scenarios:
        print(f"{scenario['name']}")
        print(f"描述: {scenario['description']}")
        
        if scenario['server_cost_price']:
            print(f"服务器成本价: {scenario['server_cost_price']:.3f}")
            data_source = 'server_cost'
            buy_price = scenario['server_cost_price']
        elif scenario['has_trade_report']:
            print("从成交回报获取: 加权平均价格 100.62")
            data_source = 'trade_report'
            buy_price = 100.62
        else:
            print("使用当前价格: 102.30 (临时)")
            data_source = 'current_price'
            buy_price = 102.30
        
        print(f"数据来源: {data_source}")
        print(f"最终买入价格: {buy_price:.3f}")
        
        # 模拟同步结果显示
        data_source_desc = {
            'trade_report': '(成交回报)',
            'server_cost': '(服务器成本价)',
            'current_price': '(当前价格)'
        }.get(data_source, '')
        
        print(f"同步结果: 🆕 发现新持仓: 123456 1000股 买入价:{buy_price:.3f} {data_source_desc}")
        print("-" * 60)

def main():
    """主测试函数"""
    print("持仓同步买入信息获取测试")
    print("=" * 50)
    
    # 测试成交回报匹配逻辑
    test_different_scenarios()
    
    print("\n" + "=" * 50)
    
    # 模拟持仓同步场景
    simulate_position_sync_scenarios()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试K线数据选择逻辑的脚本
"""

import pandas as pd
from datetime import datetime, timedelta
import numpy as np

def simulate_is_trading_day():
    """模拟交易日判断函数"""
    # 假设今天（2025-08-02）不是交易日
    return False

def create_test_kline_data():
    """创建测试K线数据"""
    # 创建最近几个交易日的30分钟K线数据
    dates = ['2025-07-31', '2025-08-01']  # 最近两个交易日
    times = ['10:00', '10:30', '11:00', '11:30', '13:30', '14:00', '14:30', '15:00']
    
    data = []
    for date in dates:
        for time in times:
            timestamp = f"{date} {time}:00"
            data.append({
                'time': timestamp,
                'open': 100 + np.random.random(),
                'high': 101 + np.random.random(),
                'low': 99 + np.random.random(),
                'close': 100 + np.random.random(),
                'volume': 1000 + np.random.randint(0, 1000)
            })
    
    df = pd.DataFrame(data)
    df['time'] = pd.to_datetime(df['time'])
    return df

def test_get_current_kline_data(df, current_hour, current_minute, debug=True):
    """测试获取当前K线数据的逻辑"""
    # 定义30分钟K线的标准时间点（K线结束时间）
    trading_klines = ['10:00', '10:30', '11:00', '11:30', '13:30', '14:00', '14:30', '15:00']

    # 首先判断今天是否为交易日
    is_today_trading_day = simulate_is_trading_day()
    
    # 计算当前应该使用的K线时间点
    target_time = None

    # 如果今天不是交易日，直接使用最新交易日的最后一根K线
    if not is_today_trading_day:
        if debug: print(f"[调试] 当前K线：今天非交易日，使用最新交易日的最后一根K线（15:00）")
        target_time = '15:00'
        # 使用最新的数据（已经是最新交易日的数据）
        
    # 今天是交易日的情况
    elif is_today_trading_day:
        # 收盘后处理（15:00之后）
        if current_hour > 15:
            if debug: print(f"[调试] 当前K线：收盘后使用今日15:00的数据")
            target_time = '15:00'

        # 15:00收盘时处理
        elif current_hour == 15 and current_minute == 0:
            if debug: print(f"[调试] 当前K线：15:00收盘时使用今日15:00的数据")
            target_time = '15:00'

        # 开盘前处理（9:30之前）
        elif current_hour < 9 or (current_hour == 9 and current_minute < 30):
            if debug: print(f"[调试] 当前K线：开盘前使用前一交易日15:00的数据")
            target_time = '15:00'
            # 获取前一交易日的数据
            today = datetime.now().strftime('%Y%m%d')
            df = df[df['time'].dt.strftime('%Y%m%d') < today]

        # 早盘9:30-10:00特殊处理
        elif current_hour == 9 and current_minute >= 30:
            if debug: print(f"[调试] 当前K线：9:30-10:00时段使用前一交易日15:00的数据（因为当前K线尚未完成）")
            target_time = '15:00'
            # 获取前一交易日的数据
            today = datetime.now().strftime('%Y%m%d')
            df = df[df['time'].dt.strftime('%Y%m%d') < today]

        # 午休时间处理（11:30-13:00）
        elif (current_hour == 11 and current_minute > 30) or (current_hour == 12) or (current_hour == 13 and current_minute == 0):
            if debug: print(f"[调试] 当前K线：午休时间使用今日11:30的数据")
            target_time = '11:30'

        # 正常交易时段
        else:
            # 根据当前时间找到对应的K线时间点
            current_time_str = f"{current_hour:02d}:{current_minute:02d}"

            # 判断当前是否在某个K线时间段内
            if current_hour == 13 and current_minute < 30:
                # 13:00-13:30时间段，当前K线尚未完成，使用上一个完整K线
                target_time = '11:30'
                if debug: print(f"[调试] 当前K线：13:00-13:30时间段，使用今日11:30的数据（当前K线未完成）")
            elif current_hour == 14 and current_minute < 30:
                # 14:00-14:30时间段，使用13:30的数据
                target_time = '13:30'
                if debug: print(f"[调试] 当前K线：14:00-14:30时间段，使用今日13:30的数据（当前K线未完成）")
            else:
                # 找到小于等于当前时间的最大K线时间点
                valid_klines = [t for t in trading_klines if t <= current_time_str]
                if valid_klines:
                    target_time = valid_klines[-1]
                    if debug: print(f"[调试] 当前K线：正常交易时段，使用今日{target_time}的数据")
                else:
                    # 如果没有找到，使用前一交易日的最后一个K线
                    target_time = '15:00'
                    today = datetime.now().strftime('%Y%m%d')
                    df = df[df['time'].dt.strftime('%Y%m%d') < today]
                    if debug: print(f"[调试] 当前K线：无有效K线时间点，使用前一交易日15:00的数据")

    # 获取目标时间的K线数据
    current_data = df[df['time'].dt.strftime('%H:%M') == target_time]
    
    if not current_data.empty:
        latest_data = current_data.iloc[-1]
        if debug: print(f"[结果] 选择的K线时间: {latest_data['time']}, 收盘价: {latest_data['close']:.3f}")
        return latest_data
    else:
        if debug: print(f"[错误] 未找到{target_time}的K线数据")
        return None

def test_get_prev_kline_data(df, current_hour, current_minute, debug=True):
    """测试获取前一根K线数据的逻辑"""
    # 首先判断今天是否为交易日
    is_today_trading_day = simulate_is_trading_day()
    
    # 计算当前应该使用的K线时间点
    target_time = None
    use_prev_day = False
    
    # 如果今天不是交易日，使用最新交易日的倒数第二根K线
    if not is_today_trading_day:
        if debug: print(f"[调试] 前一K线：今天非交易日，使用最新交易日的倒数第二根K线（14:30）")
        target_time = '14:30'
        # 使用最新的数据（已经是最新交易日的数据）
    
    # 获取目标时间的K线数据
    prev_data = df[df['time'].dt.strftime('%H:%M') == target_time]
    
    if not prev_data.empty:
        latest_data = prev_data.iloc[-1]
        if debug: print(f"[结果] 选择的前一K线时间: {latest_data['time']}, 收盘价: {latest_data['close']:.3f}")
        return latest_data
    else:
        if debug: print(f"[错误] 未找到{target_time}的前一K线数据")
        return None

def main():
    """主测试函数"""
    print("=== K线数据选择逻辑测试 ===")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"今天是否交易日: {simulate_is_trading_day()}")
    print()
    
    # 创建测试数据
    df = create_test_kline_data()
    print("创建的测试K线数据:")
    print(df[['time', 'close']].to_string(index=False))
    print()
    
    # 测试不同时间点的K线选择
    test_times = [
        (10, 50, "非交易日上午10:50"),
        (14, 20, "非交易日下午14:20"),
        (16, 30, "非交易日收盘后16:30")
    ]
    
    for hour, minute, description in test_times:
        print(f"=== 测试场景: {description} ===")
        print("当前K线选择:")
        current_kline = test_get_current_kline_data(df, hour, minute, debug=True)
        print()
        print("前一K线选择:")
        prev_kline = test_get_prev_kline_data(df, hour, minute, debug=True)
        print()
        
        if current_kline is not None and prev_kline is not None:
            print(f"✅ 成功选择K线数据")
            print(f"   当前K线: {current_kline['time']} (收盘价: {current_kline['close']:.3f})")
            print(f"   前一K线: {prev_kline['time']} (收盘价: {prev_kline['close']:.3f})")
        else:
            print(f"❌ K线数据选择失败")
        print("-" * 50)

if __name__ == "__main__":
    main()

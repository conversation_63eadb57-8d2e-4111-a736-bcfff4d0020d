#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试买入时间修复的脚本
"""

from datetime import datetime, timedelta
from xtquant import xtdata

def test_get_last_trading_day_time():
    """测试获取上一个交易日15:00时间的函数"""
    print("=== 测试获取上一个交易日15:00时间 ===")
    
    try:
        today = datetime.now().strftime('%Y%m%d')
        print(f"今天日期: {today}")
        
        # 获取当前年份的交易日历
        current_year = int(today[:4])
        start_date = f"{current_year}0101"
        end_date = f"{current_year}1231"
        
        print(f"查询交易日历: {start_date} 到 {end_date}")
        
        trading_dates = xtdata.get_trading_dates('SH', start_date, end_date)
        
        if trading_dates is not None and len(trading_dates) > 0:
            print(f"获取到 {len(trading_dates)} 个交易日")
            
            # 处理不同的返回格式
            trading_dates_str = []
            for d in trading_dates:
                if isinstance(d, str):
                    trading_dates_str.append(d)
                elif hasattr(d, 'strftime'):
                    trading_dates_str.append(d.strftime('%Y%m%d'))
                elif isinstance(d, int):
                    trading_dates_str.append(str(d))
                else:
                    trading_dates_str.append(str(d))
            
            # 排序交易日期
            trading_dates_str.sort()
            
            # 显示最近几个交易日
            print("最近的交易日:")
            recent_dates = trading_dates_str[-10:] if len(trading_dates_str) >= 10 else trading_dates_str
            for date_str in recent_dates:
                print(f"  {date_str}")
            
            # 找到今天之前的最后一个交易日
            last_trading_day = None
            for date_str in reversed(trading_dates_str):
                if date_str < today:
                    last_trading_day = date_str
                    break
            
            if last_trading_day:
                print(f"\n✅ 上一个交易日: {last_trading_day}")
                print(f"✅ 建议买入时间: {last_trading_day} 15:00:00")
                return "15:00:00"
            else:
                print("\n❌ 未找到上一个交易日")
                return "15:00:00"
        else:
            print("❌ 无法获取交易日历")
            return "15:00:00"
            
    except Exception as e:
        print(f"❌ 获取交易日历时出错: {str(e)}")
        return "15:00:00"

def simulate_position_sync_with_new_time():
    """模拟使用新时间逻辑的持仓同步"""
    print("\n=== 模拟持仓同步时间设置 ===")
    
    # 获取建议的买入时间
    default_buy_time = test_get_last_trading_day_time()
    
    # 模拟不同的同步场景
    scenarios = [
        {
            'name': '场景1：从成交回报获取信息',
            'has_trade_report': True,
            'trade_time': '10:30:15',
            'description': '使用成交回报中的真实时间'
        },
        {
            'name': '场景2：使用服务器成本价',
            'has_trade_report': False,
            'has_server_cost': True,
            'description': '使用上一个交易日15:00作为买入时间'
        },
        {
            'name': '场景3：使用当前价格',
            'has_trade_report': False,
            'has_server_cost': False,
            'description': '使用上一个交易日15:00作为买入时间'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}")
        print(f"描述: {scenario['description']}")
        
        if scenario.get('has_trade_report'):
            buy_time = scenario['trade_time']
            data_source = 'trade_report'
            print(f"✅ 买入时间: {buy_time} (来自成交回报)")
        else:
            buy_time = default_buy_time
            data_source = 'server_cost' if scenario.get('has_server_cost') else 'current_price'
            print(f"✅ 买入时间: {buy_time} (上一个交易日收盘)")
        
        print(f"数据来源: {data_source}")

def compare_old_vs_new():
    """对比修改前后的时间设置"""
    print("\n=== 修改前后对比 ===")
    
    current_time = datetime.now().strftime('%H:%M:%S')
    default_buy_time = "15:00:00"  # 上一个交易日15:00
    
    print("修改前的问题:")
    print(f"  ❌ 同步时间: {current_time} (同步执行的时间)")
    print("  ❌ 问题: 所有同步的持仓都显示相同的买入时间")
    print("  ❌ 影响: 无法反映真实的买入时间")
    
    print("\n修改后的改进:")
    print(f"  ✅ 默认时间: {default_buy_time} (上一个交易日收盘)")
    print("  ✅ 成交回报: 使用真实的成交时间")
    print("  ✅ 合理性: 反映了持仓的大致买入时间段")
    
    print("\n时间设置逻辑:")
    print("  1. 优先使用成交回报中的真实成交时间")
    print("  2. 其次使用上一个交易日15:00作为默认时间")
    print("  3. 避免使用同步执行时的当前时间")

def test_time_reasonableness():
    """测试时间合理性"""
    print("\n=== 时间合理性测试 ===")
    
    # 模拟不同时间点的同步
    sync_times = [
        "09:30:00",  # 开盘时同步
        "14:30:00",  # 交易时间同步
        "17:24:07",  # 收盘后同步（实际情况）
        "20:15:30",  # 晚上同步
    ]
    
    default_buy_time = "15:00:00"
    
    print("不同同步时间点的买入时间设置:")
    for sync_time in sync_times:
        print(f"  同步时间: {sync_time}")
        print(f"  设置买入时间: {default_buy_time} ✅")
        print(f"  合理性: 使用统一的上一交易日收盘时间")
        print()

def main():
    """主测试函数"""
    print("买入时间修复测试")
    print("=" * 50)
    
    # 测试获取上一个交易日时间
    test_get_last_trading_day_time()
    
    # 模拟持仓同步
    simulate_position_sync_with_new_time()
    
    # 对比修改前后
    compare_old_vs_new()
    
    # 测试时间合理性
    test_time_reasonableness()
    
    print("\n" + "=" * 50)
    print("✅ 修复总结:")
    print("1. 新增 get_last_trading_day_time() 函数")
    print("2. 修改持仓同步逻辑，使用上一个交易日15:00作为默认买入时间")
    print("3. 保持成交回报中真实时间的优先级")
    print("4. 避免使用同步执行时的当前时间")

if __name__ == "__main__":
    main()

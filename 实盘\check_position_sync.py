#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查持仓同步情况的脚本
"""

import json
from datetime import datetime

def analyze_position_file(file_path):
    """分析持仓记录文件"""
    print(f"=== 分析持仓文件: {file_path} ===")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            positions = json.load(f)
    except Exception as e:
        print(f"❌ 无法读取文件: {str(e)}")
        return
    
    if not positions:
        print("📝 持仓记录为空")
        return
    
    print(f"📊 总持仓数量: {len(positions)}")
    print()
    
    # 分析每个持仓
    issues = []
    normal_positions = []
    
    for code, pos_info in positions.items():
        print(f"🔍 分析 {code}:")
        
        # 检查数据结构
        if 'buy_queue' in pos_info:
            print("  ✅ 使用新的buy_queue结构")
            buy_record = pos_info['buy_queue'][0]  # 取第一个买入记录
            
            buy_price = buy_record.get('buy_price', 0)
            quantity = buy_record.get('quantity', 0)
            buy_time = buy_record.get('buy_time', '')
            order_id = buy_record.get('order_id', '')
            actual_amount = buy_record.get('actual_amount', 0)
            
            # 检查买入价格是否合理
            if buy_price < 0.1 or buy_price > 1000:
                issues.append(f"{code}: 买入价格异常 {buy_price:.6f}")
                print(f"  ❌ 买入价格异常: {buy_price:.6f}")
            else:
                print(f"  ✅ 买入价格正常: {buy_price:.3f}")
            
            # 检查数量
            print(f"  📊 持仓数量: {quantity}")
            
            # 检查买入时间
            print(f"  ⏰ 买入时间: {buy_time}")
            
            # 检查委托号
            if order_id.startswith('SERVER_SYNC_'):
                print(f"  🔄 同步生成的委托号: {order_id}")
            elif order_id.startswith('SYNC_'):
                print(f"  🔄 同步委托号: {order_id}")
            else:
                print(f"  📝 委托号: {order_id}")
            
            # 检查金额计算
            calculated_amount = buy_price * quantity
            if abs(calculated_amount - actual_amount) > 0.01:
                issues.append(f"{code}: 金额计算不一致 计算值{calculated_amount:.2f} vs 记录值{actual_amount:.2f}")
                print(f"  ❌ 金额计算不一致: 计算{calculated_amount:.2f} vs 记录{actual_amount:.2f}")
            else:
                print(f"  ✅ 金额计算一致: {actual_amount:.2f}")
            
            # 检查是否有服务器数据标记
            if buy_record.get('use_server_data'):
                print("  🌐 使用服务器数据")
            if buy_record.get('server_profit') is not None:
                server_profit = buy_record.get('server_profit', 0)
                print(f"  📈 服务器盈亏: {server_profit:.2f}")
            
        else:
            print("  ⚠️ 使用旧的数据结构")
            issues.append(f"{code}: 使用旧的数据结构")
        
        print()
    
    # 总结
    print("=" * 50)
    print("📋 分析总结:")
    print(f"  总持仓数: {len(positions)}")
    print(f"  发现问题数: {len(issues)}")
    
    if issues:
        print("\n❌ 发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    else:
        print("\n✅ 未发现明显问题")

def check_price_reasonableness():
    """检查价格合理性"""
    print("\n=== 价格合理性检查 ===")
    
    # 一些ETF的大致价格范围（用于参考）
    price_ranges = {
        '512800.SH': (0.8, 1.2),    # 银行ETF
        '513050.SH': (1.2, 1.8),    # 中概互联
        '513130.SH': (0.6, 1.0),    # 德国30
        '513560.SH': (1.2, 1.6),    # 纳斯达克
        '513920.SH': (1.4, 2.0),    # 日经225
        '513980.SH': (0.7, 1.1),    # 法国CAC40
        '518880.SH': (6.0, 9.0),    # 黄金ETF
        '159201.SZ': (0.9, 1.3),    # 军工ETF
        '159636.SZ': (1.2, 1.6),    # 创新药ETF
    }
    
    file_path = '自选股30m系统持仓记录.json'
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            positions = json.load(f)
    except Exception as e:
        print(f"❌ 无法读取文件: {str(e)}")
        return
    
    for code, pos_info in positions.items():
        if 'buy_queue' in pos_info:
            buy_price = pos_info['buy_queue'][0].get('buy_price', 0)
            
            if code in price_ranges:
                min_price, max_price = price_ranges[code]
                if min_price <= buy_price <= max_price:
                    print(f"✅ {code}: 价格{buy_price:.3f} 在合理范围[{min_price}-{max_price}]内")
                else:
                    print(f"❌ {code}: 价格{buy_price:.3f} 超出合理范围[{min_price}-{max_price}]")
            else:
                print(f"❓ {code}: 价格{buy_price:.3f} (无参考范围)")

def suggest_fixes():
    """建议修复方案"""
    print("\n=== 修复建议 ===")
    
    suggestions = [
        "1. 检查服务器返回的成本价数据格式",
        "2. 验证 成本总额/数量 的计算逻辑",
        "3. 确认服务器API返回的字段名称和数据类型",
        "4. 添加价格合理性验证",
        "5. 实现成交回报查询功能的调用",
        "6. 添加更详细的同步日志记录"
    ]
    
    for suggestion in suggestions:
        print(f"💡 {suggestion}")
    
    print("\n🔧 推荐的修复步骤:")
    print("1. 手动触发一次持仓同步，观察日志输出")
    print("2. 检查服务器API返回的原始数据格式")
    print("3. 验证成交回报查询是否正常工作")
    print("4. 如果价格异常，考虑重新同步或手动修正")

def main():
    """主函数"""
    print("持仓同步情况检查工具")
    print("=" * 50)
    
    # 分析当前持仓文件
    analyze_position_file('自选股30m系统持仓记录.json')
    
    # 检查价格合理性
    check_price_reasonableness()
    
    # 提供修复建议
    suggest_fixes()

if __name__ == "__main__":
    main()

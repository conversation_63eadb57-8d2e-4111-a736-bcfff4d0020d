# EXP4交易策略修改说明

## 📊 修改概述

已成功将自选股交易系统的买卖条件从CCI指标策略修改为EXP4指标策略，实现了更精确的技术分析和多仓位管理。

## 🔧 主要修改内容

### 1. **EXP指标体系实现**

#### **指标定义**
- **EXP1**: EMA(CLOSE, 12) - 12周期指数移动平均线
- **EXP2**: EMA(CLOSE, 50) - 50周期指数移动平均线  
- **EXP3**: REF(HHV(CLOSE,10),1) - M*REF(ATR,1) - 动态支撑阻力位
- **EXP4**: MA(EXP3, 20) - EXP3的20周期移动平均

#### **参数设置**
```python
m1 = 12  # EXP1周期
m2 = 50  # EXP2周期
n = 14   # ATR周期
p = 10   # HHV周期
q = 20   # EXP4移动平均周期
m = 1    # ATR乘数
```

### 2. **新买入条件**

#### **主要条件**
1. **价格上穿EXP4**: close > EXP4 且 前一K线close <= 前一K线EXP4
2. **多仓买入控制**: 再次买入需要比之前的最低买入价低2%

#### **买入逻辑**
- 首次买入：价格上穿EXP4即可买入10000元
- 后续买入：需要满足价格比最低买入价低2%的条件
- 支持多仓位建仓，每仓记录时间、价格、数量

### 3. **新卖出条件**

#### **主要条件**
1. **价格下破EXP4**: close < EXP4 且 前一K线close >= 前一K线EXP4
2. **盈利卖出**: 只卖出盈利的仓位
3. **多仓管理**: 有几仓盈利就卖出几仓

#### **卖出逻辑**
- 检查所有持仓仓位的盈利状况
- 只卖出当前价格高于买入价格的仓位
- 保留亏损仓位，等待后续机会

### 4. **技术实现细节**

#### **指标计算函数**
```python
def calculate_indicators(self, df):
    # 计算EXP1-EXP4指标
    # 计算价格上穿/下破EXP4信号
    # 返回包含所有指标的DataFrame
```

#### **交易条件检查**
```python
def _check_trading_conditions(self, df, current_price, code, price_time=None):
    # 检查EXP4上穿买入信号
    # 检查EXP4下破卖出信号
    # 验证多仓买入条件
    # 识别盈利仓位
```

#### **卖出执行优化**
```python
def execute_sell(self, code, price, time_str, sell_reason="EXP4_PROFIT_SELL"):
    # 识别所有盈利仓位
    # 批量卖出盈利仓位
    # 更新持仓记录
    # 记录交易历史
```

## 📈 策略特点

### **优势**
1. **自适应性强**: EXP3基于ATR动态调整，适应市场波动
2. **多仓管理**: 支持分批建仓和分批止盈
3. **风险控制**: 只在盈利时卖出，保护亏损仓位
4. **趋势跟踪**: EXP4平滑处理，减少假信号

### **风险控制**
1. **买入控制**: 多仓买入需要价格下跌2%
2. **盈利保护**: 只卖出盈利仓位
3. **强制止盈**: 保留原有的4000元强制止盈机制

## 🔄 与原CCI策略对比

| 项目 | 原CCI策略 | 新EXP4策略 |
|------|-----------|------------|
| 买入信号 | CCI上穿-100 | 价格上穿EXP4 |
| 买入条件 | 价格<EMA50 | 比最低买入价低2% |
| 卖出信号 | CCI相关条件 | 价格下破EXP4 |
| 卖出条件 | 单仓/多仓不同策略 | 统一盈利卖出策略 |
| 指标复杂度 | 中等 | 较高 |
| 自适应性 | 一般 | 强 |

## 📝 使用说明

### **启动交易**
1. 确保EXP指标计算正常
2. 设置合适的股票池
3. 启动虚拟或实盘交易模式

### **监控要点**
1. 关注EXP4指标的有效性（需要足够的历史数据）
2. 监控多仓位的盈亏状况
3. 注意强制止盈的触发情况

### **参数调整**
- 可根据市场情况调整EXP指标的周期参数
- 可调整多仓买入的价格下跌阈值（当前2%）
- 可调整强制止盈金额（当前4000元）

## ✅ 测试验证

已通过`test_exp_indicators.py`脚本验证：
- EXP1-EXP4指标计算正确
- 价格上穿/下破信号识别准确
- 指标数据有效性良好

## 🎯 后续优化建议

1. **参数优化**: 可通过历史回测优化EXP指标参数
2. **信号过滤**: 可添加成交量等辅助条件过滤信号
3. **止损机制**: 可考虑添加基于EXP指标的动态止损
4. **仓位管理**: 可优化多仓买入的资金分配策略

---

**修改完成时间**: 2025-08-02  
**修改状态**: ✅ 已完成并测试通过

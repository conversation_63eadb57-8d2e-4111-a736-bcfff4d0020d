#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EXP指标计算的脚本
"""

import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def calculate_exp_indicators(df):
    """计算EXP指标"""
    # EXP指标参数
    m1 = 12  # EXP1周期
    m2 = 50  # EXP2周期
    n = 14   # ATR周期
    p = 10   # HHV周期
    q = 20   # EXP4移动平均周期
    m = 1    # ATR乘数

    # 计算EXP1: EMA(CLOSE, 12)
    df['exp1'] = df['close'].ewm(span=m1, adjust=False).mean()

    # 计算EXP2: EMA(CLOSE, 50)
    df['exp2'] = df['close'].ewm(span=m2, adjust=False).mean()

    # 计算MTR (真实波幅)
    df['mtr'] = np.maximum(
        np.maximum(df['high'] - df['low'],
                  np.abs(df['close'].shift(1) - df['high'])),
        np.abs(df['close'].shift(1) - df['low'])
    )

    # 计算ATR: EMA(MTR, 14)
    df['atr'] = df['mtr'].ewm(span=n, adjust=False).mean()

    # 计算HHV(CLOSE, 10)
    df['hhv_close'] = df['close'].rolling(window=p).max()

    # 计算EXP3: REF(HHV(CLOSE,10),1) - M*REF(ATR,1)
    df['exp3'] = df['hhv_close'].shift(1) - m * df['atr'].shift(1)

    # 计算EXP4: MA(EXP3, 20)
    df['exp4'] = df['exp3'].rolling(window=q).mean()

    # 计算价格上穿EXP4的信号
    df['close_cross_up_exp4'] = (df['close'] > df['exp4']) & (df['close'].shift(1) <= df['exp4'].shift(1))

    # 计算价格下破EXP4的信号
    df['close_cross_down_exp4'] = (df['close'] < df['exp4']) & (df['close'].shift(1) >= df['exp4'].shift(1))

    return df

def create_test_data():
    """创建测试数据"""
    # 生成100天的模拟股价数据
    dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
    
    # 模拟股价走势
    np.random.seed(42)
    base_price = 100
    prices = []
    
    for i in range(100):
        if i == 0:
            price = base_price
        else:
            # 随机波动，带有一定趋势
            change = np.random.normal(0, 2) + 0.1 * np.sin(i * 0.1)
            price = max(prices[-1] + change, 50)  # 最低价格50
        prices.append(price)
    
    # 生成高低开收数据
    data = []
    for i, price in enumerate(prices):
        high = price + np.random.uniform(0, 3)
        low = price - np.random.uniform(0, 3)
        open_price = price + np.random.uniform(-1, 1)
        close_price = price
        
        data.append({
            'date': dates[i],
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': np.random.randint(1000, 10000)
        })
    
    return pd.DataFrame(data)

def test_exp_indicators():
    """测试EXP指标计算"""
    print("=== EXP指标计算测试 ===")
    
    # 创建测试数据
    df = create_test_data()
    print(f"创建了 {len(df)} 天的测试数据")
    
    # 计算EXP指标
    df = calculate_exp_indicators(df)
    
    # 显示最后10天的数据
    print("\n最后10天的EXP指标数据:")
    columns = ['date', 'close', 'exp1', 'exp2', 'exp3', 'exp4', 'close_cross_up_exp4', 'close_cross_down_exp4']
    print(df[columns].tail(10).to_string(index=False, float_format='%.3f'))
    
    # 检查信号
    buy_signals = df[df['close_cross_up_exp4'] == True]
    sell_signals = df[df['close_cross_down_exp4'] == True]
    
    print(f"\n发现 {len(buy_signals)} 个买入信号（价格上穿EXP4）")
    print(f"发现 {len(sell_signals)} 个卖出信号（价格下破EXP4）")
    
    if len(buy_signals) > 0:
        print("\n买入信号详情:")
        for _, signal in buy_signals.iterrows():
            print(f"  {signal['date'].strftime('%Y-%m-%d')}: 价格 {signal['close']:.2f}, EXP4 {signal['exp4']:.2f}")
    
    if len(sell_signals) > 0:
        print("\n卖出信号详情:")
        for _, signal in sell_signals.iterrows():
            print(f"  {signal['date'].strftime('%Y-%m-%d')}: 价格 {signal['close']:.2f}, EXP4 {signal['exp4']:.2f}")
    
    # 验证指标计算的有效性
    print("\n=== 指标有效性检查 ===")
    valid_exp1 = df['exp1'].notna().sum()
    valid_exp2 = df['exp2'].notna().sum()
    valid_exp3 = df['exp3'].notna().sum()
    valid_exp4 = df['exp4'].notna().sum()
    
    print(f"EXP1有效数据: {valid_exp1}/{len(df)}")
    print(f"EXP2有效数据: {valid_exp2}/{len(df)}")
    print(f"EXP3有效数据: {valid_exp3}/{len(df)}")
    print(f"EXP4有效数据: {valid_exp4}/{len(df)}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_exp_indicators()

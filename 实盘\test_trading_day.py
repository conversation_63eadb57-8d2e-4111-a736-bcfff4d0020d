#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易日判断功能的脚本
"""

from datetime import datetime, timedelta
from xtquant import xtdata

def test_trading_day_function():
    """测试交易日判断功能"""
    
    def is_trading_day(date=None):
        """检查指定日期是否为交易日"""
        try:
            if date is None:
                date = datetime.now().strftime('%Y%m%d')
            elif isinstance(date, datetime):
                date = date.strftime('%Y%m%d')
            
            # 使用xtdata.get_trading_dates获取交易日历
            # 获取当前年份的交易日历
            current_year = int(date[:4])
            start_date = f"{current_year}0101"
            end_date = f"{current_year}1231"
            
            print(f"正在获取{current_year}年的交易日历...")
            trading_dates = xtdata.get_trading_dates('SH', start_date, end_date)
            
            if trading_dates is not None and len(trading_dates) > 0:
                # 将日期转换为字符串格式进行比较
                trading_dates_str = [d.strftime('%Y%m%d') for d in trading_dates]
                print(f"获取到{len(trading_dates_str)}个交易日")
                return date in trading_dates_str, trading_dates_str
            else:
                print("无法获取交易日历，使用简单的工作日判断作为备选")
                # 如果无法获取交易日历，使用简单的工作日判断作为备选
                date_obj = datetime.strptime(date, '%Y%m%d')
                # 周一到周五为工作日（0-4），周六周日为休息日（5-6）
                return date_obj.weekday() < 5, []
                
        except Exception as e:
            print(f"检查交易日时出错: {str(e)}")
            # 出错时使用简单的工作日判断作为备选
            try:
                if date is None:
                    date_obj = datetime.now()
                else:
                    date_obj = datetime.strptime(date, '%Y%m%d') if isinstance(date, str) else date
                return date_obj.weekday() < 5, []
            except:
                return False, []

    def get_trading_status():
        """获取当前交易状态的详细信息"""
        now = datetime.now()
        current_date = now.strftime('%Y%m%d')
        current_time = now.time()
        
        # 检查是否为交易日
        is_trading_day_result, trading_dates = is_trading_day(current_date)
        
        # 检查是否在交易时段
        in_trading_hours = (('09:30:00' <= current_time.strftime('%H:%M:%S') <= '11:30:00') or
                           ('13:00:00' <= current_time.strftime('%H:%M:%S') <= '15:00:00'))
        
        # 检查是否在开盘前时段（9:00-9:30）
        in_pre_market = '09:00:00' <= current_time.strftime('%H:%M:%S') <= '09:30:00'
        
        # 检查是否在收盘后时段（15:00-15:30）
        in_post_market = '15:00:00' <= current_time.strftime('%H:%M:%S') <= '15:30:00'
        
        status = {
            'is_trading_day': is_trading_day_result,
            'in_trading_hours': in_trading_hours,
            'in_pre_market': in_pre_market,
            'in_post_market': in_post_market,
            'can_trade': is_trading_day_result and in_trading_hours,
            'current_time': now.strftime('%Y-%m-%d %H:%M:%S'),
            'weekday': now.strftime('%A'),
            'date_str': current_date,
            'trading_dates_count': len(trading_dates)
        }
        
        # 生成状态描述
        if not is_trading_day_result:
            if now.weekday() >= 5:  # 周末
                status['description'] = f"今天是{status['weekday']}，非交易日"
            else:
                status['description'] = "今天是工作日但非交易日（可能是节假日）"
        elif in_trading_hours:
            status['description'] = "交易时间"
        elif in_pre_market:
            status['description'] = "开盘前时段（9:00-9:30）"
        elif in_post_market:
            status['description'] = "收盘后时段（15:00-15:30）"
        else:
            status['description'] = "非交易时段"
            
        return status

    print("=== 交易日判断功能测试 ===")
    
    # 测试当前日期
    print("\n1. 测试当前日期:")
    status = get_trading_status()
    print(f"当前时间: {status['current_time']}")
    print(f"星期: {status['weekday']}")
    print(f"是否交易日: {status['is_trading_day']}")
    print(f"是否交易时间: {status['can_trade']}")
    print(f"状态描述: {status['description']}")
    print(f"获取到的交易日数量: {status['trading_dates_count']}")
    
    # 测试最近几天
    print("\n2. 测试最近7天:")
    for i in range(7):
        test_date = datetime.now() - timedelta(days=i)
        date_str = test_date.strftime('%Y%m%d')
        is_trading, _ = is_trading_day(date_str)
        weekday = test_date.strftime('%A')
        print(f"{test_date.strftime('%Y-%m-%d')} ({weekday}): {'✅ 交易日' if is_trading else '❌ 非交易日'}")
    
    # 测试特定日期（一些已知的节假日）
    print("\n3. 测试特定日期:")
    test_dates = [
        '20250101',  # 元旦
        '20250203',  # 春节假期
        '20250505',  # 劳动节
        '20250101',  # 国庆节
    ]
    
    for date_str in test_dates:
        try:
            test_date = datetime.strptime(date_str, '%Y%m%d')
            is_trading, _ = is_trading_day(date_str)
            weekday = test_date.strftime('%A')
            print(f"{test_date.strftime('%Y-%m-%d')} ({weekday}): {'✅ 交易日' if is_trading else '❌ 非交易日'}")
        except:
            print(f"{date_str}: 日期格式错误")

if __name__ == "__main__":
    test_trading_day_function()

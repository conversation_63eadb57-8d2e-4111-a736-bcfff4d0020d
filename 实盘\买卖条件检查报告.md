# 买卖条件检查报告

## 📋 **检查时间**: 2025-08-02

## ✅ **买入条件检查**

### **要求的买入条件**
1. ✅ **close上穿exp4时买入一仓（10000元）**
2. ✅ **可多仓买入，记录每仓时间价格数量**
3. ✅ **再次买入需要比之前的最低买入价低2%**

### **实际实现检查**

#### **1. 价格上穿EXP4检测** ✅
```python
# 计算价格上穿EXP4的信号
df['close_cross_up_exp4'] = (df['close'] > df['exp4']) & (df['close'].shift(1) <= df['exp4'].shift(1))

# 检查价格上穿EXP4信号
close_cross_up_exp4 = current_kline_data['close_cross_up_exp4'].iloc[-1]
exp4_cross_condition = close_cross_up_exp4
```
**状态**: ✅ **正确** - 准确检测当前K线收盘价上穿EXP4且前一K线收盘价未上穿

#### **2. 多仓买入价格控制** ✅
```python
# 获取最低买入价格（用于多仓买入判断）
min_buy_price = None
if code in self.position_records:
    position_info = self.position_records[code]
    buy_queue = position_info.get('buy_queue', [])
    if buy_queue:
        min_buy_price = min(buy['buy_price'] for buy in buy_queue)

# 条件2：如果已有持仓，当前价格需要比最低买入价低2%
price_drop_condition = True
if min_buy_price is not None:
    price_drop = (min_buy_price - current_price) / min_buy_price * 100
    price_drop_condition = price_drop >= 2.0
```
**状态**: ✅ **正确** - 首次买入无限制，后续买入需比最低买入价低2%

#### **3. 买入信号综合判断** ✅
```python
# 买入信号判断：close上穿EXP4 且 (首次买入或价格比最低买入价低2%)
buy_signal = exp4_cross_condition and price_drop_condition
```
**状态**: ✅ **正确** - 同时满足价格上穿EXP4和价格控制条件

#### **4. 买入金额和记录** ✅
- **买入金额**: 每次买入目标金额10000元 ✅
- **记录管理**: 详细记录每仓的时间、价格、数量 ✅
- **多仓支持**: 支持多次买入建仓 ✅

---

## ✅ **卖出条件检查**

### **要求的卖出条件**
1. ✅ **股价下破exp4时卖出**
2. ✅ **但需盈利，如为多仓，有几仓盈利就卖出几仓**

### **实际实现检查**

#### **1. 价格下破EXP4检测** ✅
```python
# 计算价格下破EXP4的信号
df['close_cross_down_exp4'] = (df['close'] < df['exp4']) & (df['close'].shift(1) >= df['exp4'].shift(1))

# 检查价格下破EXP4信号
close_cross_down_exp4 = current_kline_data['close_cross_down_exp4'].iloc[-1]
```
**状态**: ✅ **正确** - 准确检测当前K线收盘价下破EXP4且前一K线收盘价未下破

#### **2. 盈利仓位识别** ✅
```python
if buy_queue and close_cross_down_exp4:
    # 新的卖出逻辑：股价下破EXP4时，卖出所有盈利的仓位
    profitable_positions = []
    total_profitable_quantity = 0
    
    for buy_record in buy_queue:
        buy_price = buy_record['buy_price']
        quantity = buy_record['quantity']
        # 计算当前价格相对于买入价的涨幅
        price_increase = (current_price - buy_price) / buy_price * 100
        is_profitable = price_increase > 0
        
        if is_profitable:
            profitable_positions.append({
                'buy_price': buy_price,
                'quantity': quantity,
                'price_increase': price_increase,
                'buy_record': buy_record
            })
            total_profitable_quantity += quantity
```
**状态**: ✅ **正确** - 逐个检查每仓的盈利状况，只选择盈利仓位

#### **3. 卖出执行逻辑** ✅
```python
# 如果有盈利仓位，则触发卖出信号
if profitable_positions:
    sell_condition_met = True
    sell_condition_type = "EXP4_PROFIT_SELL"
else:
    # 没有盈利仓位，不卖出
    sell_info = {
        'type': 'HOLD',
        'sell_condition_met': False,
        'reason': 'no_profitable_positions'
    }
```
**状态**: ✅ **正确** - 只有存在盈利仓位时才触发卖出

#### **4. 多仓卖出处理** ✅
在`execute_sell`函数中：
```python
# 新策略：找出所有盈利的仓位并卖出
profitable_positions = []
total_sell_quantity = 0

for i, buy_record in enumerate(buy_queue):
    buy_price = buy_record['buy_price']
    quantity = buy_record['quantity']
    profit_per_share = price - buy_price
    is_profitable = profit_per_share > 0
    
    if is_profitable:
        profitable_positions.append({
            'index': i,
            'buy_record': buy_record,
            'quantity': quantity,
            'buy_price': buy_price,
            'profit_per_share': profit_per_share
        })
        total_sell_quantity += quantity
```
**状态**: ✅ **正确** - 有几仓盈利就卖出几仓

---

## 🔍 **EXP指标计算检查**

### **EXP指标参数** ✅
```python
m1 = 12  # EXP1周期
m2 = 50  # EXP2周期
n = 14   # ATR周期
p = 10   # HHV周期
q = 20   # EXP4移动平均周期
m = 1    # ATR乘数
```
**状态**: ✅ **正确** - 参数设置符合要求

### **EXP4计算公式** ✅
```python
# EXP1: EMA(CLOSE, 12)
df['exp1'] = df['close'].ewm(span=m1, adjust=False).mean()

# EXP2: EMA(CLOSE, 50)
df['exp2'] = df['close'].ewm(span=m2, adjust=False).mean()

# MTR (真实波幅)
df['mtr'] = np.maximum(
    np.maximum(df['high'] - df['low'],
              np.abs(df['close'].shift(1) - df['high'])),
    np.abs(df['close'].shift(1) - df['low'])
)

# ATR: EMA(MTR, 14)
df['atr'] = df['mtr'].ewm(span=n, adjust=False).mean()

# HHV(CLOSE, 10)
df['hhv_close'] = df['close'].rolling(window=p).max()

# EXP3: REF(HHV(CLOSE,10),1) - M*REF(ATR,1)
df['exp3'] = df['hhv_close'].shift(1) - m * df['atr'].shift(1)

# EXP4: MA(EXP3, 20)
df['exp4'] = df['exp3'].rolling(window=q).mean()
```
**状态**: ✅ **正确** - 完全按照公式实现

---

## 🚫 **已移除的旧条件**

### **已移除的CCI相关判断** ✅
- ❌ CCI上穿-100买入条件
- ❌ CCI连续大于100卖出条件
- ❌ EMA20/EMA50相关条件
- ❌ 单仓/多仓不同卖出策略

**状态**: ✅ **已完全移除** - 不再使用CCI等旧指标

---

## 📊 **总体检查结果**

| 检查项目 | 要求 | 实现状态 | 符合度 |
|---------|------|----------|--------|
| 买入触发条件 | close上穿EXP4 | ✅ 已实现 | 100% |
| 买入金额 | 10000元/仓 | ✅ 已实现 | 100% |
| 多仓买入控制 | 比最低买入价低2% | ✅ 已实现 | 100% |
| 卖出触发条件 | close下破EXP4 | ✅ 已实现 | 100% |
| 盈利卖出限制 | 只卖出盈利仓位 | ✅ 已实现 | 100% |
| 多仓卖出逻辑 | 有几仓盈利卖几仓 | ✅ 已实现 | 100% |
| 旧条件移除 | 移除CCI等判断 | ✅ 已移除 | 100% |
| 记录格式保持 | 保持原有格式 | ✅ 已保持 | 100% |

## ✅ **最终结论**

**所有买卖条件均已正确实现，完全符合您的要求！**

- ✅ 买入条件：价格上穿EXP4时买入10000元，支持多仓，后续买入需比最低买入价低2%
- ✅ 卖出条件：价格下破EXP4时卖出，但只卖出盈利仓位，有几仓盈利就卖几仓
- ✅ 已完全移除CCI等旧的判断条件
- ✅ 保持了原有的记录格式和其他功能

**系统已准备就绪，可以开始使用新的EXP4交易策略！**

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接修复持仓记录中的买入时间
"""

import json
from datetime import datetime

def direct_fix():
    """直接修复买入时间"""
    
    file_path = '自选股30m系统持仓记录.json'
    
    print("=== 直接修复买入时间 ===")
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"原文件大小: {len(content)} 字符")
        
        # 解析JSON
        positions = json.loads(content)
        print(f"解析到 {len(positions)} 个持仓")
        
        # 修复每个持仓的买入时间
        for code, pos_info in positions.items():
            if 'buy_queue' in pos_info and pos_info['buy_queue']:
                buy_record = pos_info['buy_queue'][0]
                old_time = buy_record.get('buy_time', '')
                
                # 设置为15:00:00
                buy_record['buy_time'] = '15:00:00'
                buy_record['time_fixed'] = True
                buy_record['original_time'] = old_time
                
                print(f"修复 {code}: {old_time} → 15:00:00")
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(positions, f, ensure_ascii=False, indent=2)
        
        print("✅ 文件已更新")
        
        # 验证
        with open(file_path, 'r', encoding='utf-8') as f:
            verify_data = json.load(f)
        
        first_code = list(verify_data.keys())[0]
        first_time = verify_data[first_code]['buy_queue'][0]['buy_time']
        print(f"验证: {first_code} 的买入时间现在是 {first_time}")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    direct_fix()
